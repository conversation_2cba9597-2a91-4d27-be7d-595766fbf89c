<template>
  <div class="activity">
    <img src="https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG34fae479830af88be0c3b3fae515b3c4.png" alt="">
    <div class="titlediv">
        <el-text truncated class="title">中职语文（上）</el-text>
        <el-dropdown
         :hide-on-click="true"
         trigger="click"
        @command="handleCommand">
            <img src="@/assets/center/mbtnicon.png" alt="">
            <template #dropdown>
                <el-dropdown-menu>
                    <el-dropdown-item :command="1">编辑</el-dropdown-item>
                    <el-dropdown-item v-if="item.type == 1" :command="3">开始直播</el-dropdown-item>
                    <el-dropdown-item v-if="item.type == 1" :command="4">结束直播</el-dropdown-item>
                    <el-dropdown-item v-if="item.type == 1" :command="5">报名信息</el-dropdown-item>
                    <el-dropdown-item v-if="item.type == 1" :command="6">回放管理</el-dropdown-item>
                    <el-dropdown-item :command="2">删除</el-dropdown-item>
                </el-dropdown-menu>
            </template>
        </el-dropdown>
    </div>
    <div class="msgdiv">
        <el-text truncated tag="p">活动类型：{{activityTypesMap[item.type]}}</el-text>
        <el-text truncated tag="p">创建时间：2025-07-31 12:22:43</el-text>
    </div>
    <span :class="activityStatusColorMap[item.status]" class="tagcss">{{ activityStatusMap[item.status] }}</span>
    <msg-dialog ref="msgdialogRef" />
    <addPlanback ref="addplanbackRef"/>
  </div>
</template>

<script setup>
import { deleteTeachingOffice } from "@/api/expert/index";
import { activityTypesMap,activityStatusColorMap,activityStatusMap } from "@/utils/static-tools";
import addPlanback from './addPlanback.vue';
import { useRouter } from "vue-router";
const router = useRouter()
const props = defineProps({
    item:{
        type:Object,
        default:()=>{
            return new Object()
        }
    }
})
const emits = defineEmits(['reload'])
const addplanbackRef = ref()
const msgdialogRef = ref()
function handleDelete(row) {
    msgdialogRef.value.show({
        type:'edit',
        title:'删除',
        msg:'确认删除该教研室吗？',
        submitBtnText:"确认删除",
        submitAction:()=>{
            console.log('cccccc====',row);
        }
    })
}
function handleCommand(command){

    if (command == 1) {
        router.push({
            path:'/jiaoyanshi/addActivity',
            query:{
                id:1122
            }
        })
    }else if(command == 2){
        console.log('aaaaaaa',command);
        handleDelete(props.item)
    }else if (command == 3){
        // 开播
    }else if (command == 4){
        // 结束
    }else if(command == 5){
        // 报名人员
    }else if(command == 6){
        addplanbackRef.value.show()
    }
}
</script>

<style lang="scss" scoped>
.tagcss{
    padding: 4px 12px;
    border-radius: 4px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 13px;
    color: #FFFFFF;
    text-align: center;
    font-style: normal;
    text-transform: none;
    position: absolute;
    top: 8px;
    left: 8px;
}
.activity{
    background-color: #FFFFFF;
    position: relative;
}
.activity img{
    width: 100%;
    aspect-ratio: 16/9;
}
.titlediv{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin:8px 8px  8px 8px;
    .title{
        width: calc(100% - 30px);
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 16px;
        color: #44474D;
        text-align: left;
        font-style: normal;
        text-transform: none;
    }
}
.titlediv img{
    width: 24px;
    height: 24px;
}
.msgdiv{
    margin:0px 8px  20px 8px;
    p{
        height: 20px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 13px;
        color: #878D99;
        text-align: left;
        font-style: normal;
        text-transform: none;
        margin-bottom: 8px;
        display: block !important;
    }
    
}
</style>