// 文本省略
.text-ellipsis-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  white-space: normal;
}

.text-ellipsis-3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  white-space: normal;
}

:root {
  --primary-bg-color: #EAEBFF;
  --primary-text-gradient: linear-gradient(180.00000000000006deg, #8163EE 0%, #1872FF 100%);
  ;
  --primary-btnbg-color: linear-gradient(270deg, #B892CE 0%, #8677EC 100%);
  --plyr-range-fill-background: #387FF5;
}

.primary-text-color {
  background: var(--primary-text-gradient);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
  cursor: pointer;
}

/* 分页组件全局样式 */
.el-pagination {

  .el-pagination__total,
  .el-pagination__jump {
    font-weight: 400;
    font-size: 14px;
    color: #2A2D33;

  }

  .el-pager li {
    background-color: #FFFFFF !important;
    font-size: 14px;
    min-width: 32px;
    height: 32px;
    line-height: 32px;
    border-radius: 4px;
    margin: 0 4px;
    border: 1px solid #D8E2EB;
    font-family: Source Han Sans CN, Source Han Sans CN;

    &:hover {
      color: #1872FF;
    }

    &.is-active {
      background: #386CFC !important;
      border: 1px solid #386CFC !important;
      font-weight: 500;
      font-size: 14px;
      color: #FFFFFF;
      text-align: center;
    }
  }

  .btn-prev,
  .btn-next {
    background-color: #FFFFFF !important;
    border-radius: 4px;
    padding: 0 8px;
    height: 32px;
    line-height: 32px;
    border: 1px solid #D8E2EB;
    font-family: Source Han Sans CN, Source Han Sans CN;

    &:hover {
      color: #1872FF;
    }
  }
}

.main {
  width: 1160px;
  margin: 0 auto;
  height: 100%;
}

.el-message {
  top: 20% !important;
  margin-top: -100px !important;
  z-index: 100000002 !important;
}

.w1440 {
  width: 1440px;
  margin: 0 auto;
}

.w1280 {
  width: 1280px;
  margin: 0 auto;
}

#app {
  min-width: 1200px;
}

li {
  list-style: none;
}

.linebtn {
  min-width: 96px !important;
  height: 40px !important;
  border-radius: 4px;
  border: 1px solid #386CFC !important;
  text-align: center !important;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 500;
  font-size: 16px !important;
  color: #386CFC !important;
  font-style: normal;
  text-transform: none;
}

.defbtn40 {
  background: #386CFC !important;
  min-width: 92px;
  height: 40px !important;
  padding: 8px 12px !important;
  border: 1px solid #1872FF !important;
  border-radius: 4px !important;
  font-family: Source Han Sans CN, Source Han Sans CN !important;
  font-weight: 400 !important;
  font-size: 16px !important;
  color: #FFFFFF !important;
  font-style: normal;
  text-transform: none;
}

.defbtn {
  background: #386CFC !important;
  height: 36px !important;
  padding: 9px 12px !important;
  border-radius: 4px !important;
  font-family: Source Han Sans CN, Source Han Sans CN !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  color: #FFFFFF !important;
  font-style: normal;
  text-transform: none;

}

// dropdown
.el-dropdown-menu {
  min-width: 88px !important;
  border-radius: 8px;
  text-align: center;
  overflow: hidden;
  padding: 0 !important;
}

/* 消除小三角 */
.el-popper__arrow::before {
  width: 0px !important;
  height: 0px !important;
}

.el-popper[x-placement^=bottom] .popper__arrow {
  border: none !important;
}

.el-popper[x-placement^=bottom] .popper__arrow::before {
  border: none !important;
}

// 下拉框距离选择框的间隙
.el-popper[x-placement^=bottom] {
  margin-top: 0px;
}

.el-dropdown-menu__item {
  width: 100% !important;
  height: 40px !important;
  line-height: 40px !important;
  justify-content: center;
  text-align: center;
  margin: 0 auto;
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}

.commonSelect {
  height: 40px;

  .el-select__wrapper {
    height: 40px;
    line-height: 40px;
  }
}

.el-dropdown-menu__item:hover,
.el-dropdown-item:focus {
  background-color: #4285F4 !important;
  color: white !important;
}

// el-select
.el-select-dropdown__list {
  padding: 0px 0px !important;

  .el-select-dropdown__item {
    height: 42px;
    line-height: 42px;
  }

  .is-selected,
  .is-hovering {
    background-color: #4285F4 !important;
    color: white !important;
  }
}



.adddialog {

  padding: 0px !important;
  padding-bottom: 30px !important;
  overflow: hidden;
  border-radius: 8px !important;

  .el-dialog__headerbtn {
    width: 24px !important;
    height: 24px !important;
    right: 30px;
    top: 24px;

    .el-icon {
      width: 24px;
      height: 24px;
      font-size: 20px !important;
      font-weight: 500 !important;
      color: #2D2F33 !important;
    }
  }

  .el-dialog__body {
    padding: 30px;
  }

  .el-dialog__header {
    padding: 23px 30px;
    background: linear-gradient(180deg, #E7EDFE 0%, rgba(231, 237, 254, 0) 100%) !important;
  }
}

.content-container {
  background: linear-gradient(90deg, #F5F8FC 0%, #F7F5FC 100%);
}

.mytable th {
  background-color: #f6f7fb !important;
  height: 52px;
  line-height: 52px;
  border: none;
}

.mytable td {
  border-bottom: 1px solid #f0f0f0 !important;
  padding: 0;
}

.mytable th {
  padding: 0;
  border: 0 !important;
}

.mytable th>.cell {
  font-size: 14px;
  font-family: Microsoft YaHei-Regular, Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}

.mytable {
  border: 1px solid #E1E4EB;
  border-radius: 8px;
}

.mytable::before {
  height: 0px !important;
}

.mytable .el-table__fixed-right::before {
  height: 0px !important;
}

.el-menu--horizontal>.el-menu-item.is-active {
  border-bottom: none !important;
  color: #386cfc !important;
  font-weight: 500 !important;
  position: relative;
}

.el-menu--horizontal>.el-menu-item.is-active::after {
  content: '';
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
  height: 3px;
  background-color: #386cfc;
  border-radius: 5px;
  transition: all 0.3s ease;
}

.el-menu--horizontal>.el-menu-item {
  transition: color 0.3s ease !important;
  border-bottom: none !important;
}

.el-menu--horizontal>.el-submenu.is-active .el-submenu__title {
  border-bottom: none !important;
  color: #386cfc !important;
  font-weight: 500 !important;
  position: relative;
}

.el-menu--horizontal>.el-submenu.is-active .el-submenu__title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
  height: 3px;
  background-color: #386cfc;
  border-radius: 5px;
  transition: all 0.3s ease;
}

/* 未选中状态的样式 */
.el-menu--horizontal>.el-menu-item {
  font-family: Source Han Sans CN, Source Han Sans CN !important;
  font-weight: 400 !important;
  font-size: 16px !important;
  color: #2d2f33 !important;
}

/* 鼠标悬停状态的样式 */
.el-menu--horizontal>.el-menu-item:hover {
  color: #386cfc !important;
  /* 悬停时的文字颜色 */
  background: #fff !important;
}

.el-menu--horizontal>.el-menu-item {
  padding: 0 30px !important;
  margin: 0 5px !important;
}

.el-menu.el-menu--horizontal {
  border-bottom: 0 !important;
}

.el-menu--horizontal {
  white-space: nowrap !important;
  overflow-x: auto !important;
  /* 允许横向滚动 */
}

.el-menu--horizontal::-webkit-scrollbar {
  display: none;
  /* 隐藏滚动条 */
}

.el-menu--horizontal>.el-menu-item {
  height: 60px;
  line-height: 60px;
}

.el-menu {
  width: 100%;
  display: flex;
}

.el-menu--horizontal {
  white-space: nowrap !important;
  overflow-x: auto !important;
}

.el-menu--horizontal::-webkit-scrollbar {
  display: none;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

/* 自定义滚动条滑块样式 */
::-webkit-scrollbar-thumb {
  background-color: #DBDBDB;
  border-radius: 5px;
}

/* 自定义滚动条轨道样式 */
::-webkit-scrollbar-track {
  background-color: #f1f1f1;
  /* 轨道颜色 */
}

.wid100 {
  width: 100% !important;
}

.c1 {
  background-color: #36CB57;
}

.c2 {
  background-color: #FC8F1A;
}

.c3 {
  background-color: #878D99;
}

.definput {
  width: 280px !important;
  min-height: 36px;
  border-radius: 4px;

  :v-deep(.el-input__inner) {
    border: 1px solid #E1E4EB;
  }

  ::v-deep(.el-select__wrapper) {
    min-height: 36px !important;
  }
}